---
sidebar_position: 40
---

# Configuration Properties

Configuration properties for the gRPC starter project.

This page was generated by [spring-configuration-property-documenter](https://github.com/rodnansol/spring-configuration-property-documenter/blob/master/docs/modules/ROOT/pages/gradle-plugin.adoc).

## Table of Contents
* [**grpc-client-boot-autoconfigure**](#grpc-client-boot-autoconfigure)
  * [**grpc.client.tls.key-manager** - `grpcstarter.client.GrpcClientProperties$Tls$KeyManager`](#grpc.client.tls.key-manager)

  * [**grpc.client.tls.trust-manager** - `grpcstarter.client.GrpcClientProperties$Tls$TrustManager`](#grpc.client.tls.trust-manager)

  * [**grpc.client** - `grpcstarter.client.GrpcClientProperties`](#grpc.client)

  * [**grpc.client.in-process** - `grpcstarter.client.GrpcClientProperties$InProcess`](#grpc.client.in-process)

  * [**grpc.client.refresh** - `grpcstarter.client.GrpcClientProperties$Refresh`](#grpc.client.refresh)

  * [**grpc.client.retry** - `grpcstarter.client.GrpcClientProperties$Retry`](#grpc.client.retry)

  * [**grpc.client.tls** - `grpcstarter.client.GrpcClientProperties$Tls`](#grpc.client.tls)
* [**grpc-server-boot-autoconfigure**](#grpc-server-boot-autoconfigure)
  * [**grpc.server.tls.key-manager** - `grpcstarter.server.GrpcServerProperties$Tls$KeyManager`](#grpc.server.tls.key-manager)

  * [**grpc.server.tls.trust-manager** - `grpcstarter.server.GrpcServerProperties$Tls$TrustManager`](#grpc.server.tls.trust-manager)

  * [**grpc.server.health.datasource** - `grpcstarter.server.GrpcServerProperties$Health$DataSource`](#grpc.server.health.datasource)

  * [**grpc.server.health.redis** - `grpcstarter.server.GrpcServerProperties$Health$Redis`](#grpc.server.health.redis)

  * [**grpc.server** - `grpcstarter.server.GrpcServerProperties`](#grpc.server)

  * [**grpc.server.channelz** - `grpcstarter.server.GrpcServerProperties$Channelz`](#grpc.server.channelz)

  * [**grpc.server.exception-handling** - `grpcstarter.server.GrpcServerProperties$ExceptionHandling`](#grpc.server.exception-handling)

  * [**grpc.server.health** - `grpcstarter.server.GrpcServerProperties$Health`](#grpc.server.health)

  * [**grpc.server.in-process** - `grpcstarter.server.GrpcServerProperties$InProcess`](#grpc.server.in-process)

  * [**grpc.server.reflection** - `grpcstarter.server.GrpcServerProperties$Reflection`](#grpc.server.reflection)

  * [**grpc.server.response** - `grpcstarter.server.GrpcServerProperties$Response`](#grpc.server.response)

  * [**grpc.server.tls** - `grpcstarter.server.GrpcServerProperties$Tls`](#grpc.server.tls)
* [**grpc-metrics**](#grpc-metrics)
  * [**grpc.metrics** - `grpcstarter.extensions.metrics.GrpcMetricsProperties`](#grpc.metrics)

  * [**grpc.metrics.client** - `grpcstarter.extensions.metrics.GrpcMetricsProperties$Client`](#grpc.metrics.client)

  * [**grpc.metrics.server** - `grpcstarter.extensions.metrics.GrpcMetricsProperties$Server`](#grpc.metrics.server)
* [**grpc-test**](#grpc-test)
  * [**grpc.test** - `grpcstarter.extensions.test.GrpcTestProperties`](#grpc.test)

  * [**grpc.test.server** - `grpcstarter.extensions.test.GrpcTestProperties$Server`](#grpc.test.server)
* [**grpc-tracing**](#grpc-tracing)
  * [**grpc.tracing** - `grpcstarter.extensions.tracing.GrpcTracingProperties`](#grpc.tracing)

  * [**grpc.tracing.client** - `grpcstarter.extensions.tracing.GrpcTracingProperties$Client`](#grpc.tracing.client)

  * [**grpc.tracing.server** - `grpcstarter.extensions.tracing.GrpcTracingProperties$Server`](#grpc.tracing.server)
* [**grpc-transcoding**](#grpc-transcoding)
  * [**grpc.transcoding** - `grpcstarter.extensions.transcoding.GrpcTranscodingProperties`](#grpc.transcoding)

  * [**grpc.transcoding.print-options** - `grpcstarter.extensions.transcoding.GrpcTranscodingProperties$PrintOptions`](#grpc.transcoding.print-options)
* [**grpc-validation**](#grpc-validation)
  * [**grpc.validation** - `grpcstarter.extensions.validation.GrpcValidationProperties`](#grpc.validation)

  * [**grpc.validation.client** - `grpcstarter.extensions.validation.GrpcValidationProperties$Client`](#grpc.validation.client)

  * [**grpc.validation.server** - `grpcstarter.extensions.validation.GrpcValidationProperties$Server`](#grpc.validation.server)

## grpc-client-boot-autoconfigure
### grpc.client.tls.key-manager
**Class:** `grpcstarter.client.GrpcClientProperties$Tls$KeyManager`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| cert-chain| org.springframework.core.io.Resource| | | | 
| private-key| org.springframework.core.io.Resource| | | | 
| private-key-password| java.lang.String| | | | 
### grpc.client.tls.trust-manager
**Class:** `grpcstarter.client.GrpcClientProperties$Tls$TrustManager`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| root-certs| org.springframework.core.io.Resource| | | | 
### grpc.client
**Class:** `grpcstarter.client.GrpcClientProperties`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| authority| java.lang.String| | | | 
| base-packages| java.util.List&lt;java.lang.String&gt;| | | | 
| bean-definition-handler| java.lang.Class&lt;? extends grpcstarter.client.GrpcClientBeanDefinitionHandler&gt;| | | | 
| channels| java.util.List&lt;grpcstarter.client.GrpcClientProperties$Channel&gt;| | | | 
| clients| java.util.List&lt;java.lang.Class&lt;? extends io.grpc.stub.AbstractStub&gt;&gt;| | | | 
| compression| java.lang.String| | | | 
| deadline| java.lang.Long| | | | 
| enabled| java.lang.Boolean| | | | 
| max-inbound-message-size| org.springframework.util.unit.DataSize| | | | 
| max-inbound-metadata-size| org.springframework.util.unit.DataSize| | | | 
| max-outbound-message-size| org.springframework.util.unit.DataSize| | | | 
| metadata| java.util.List&lt;grpcstarter.client.GrpcClientProperties$Metadata&gt;| | | | 
| shutdown-timeout| java.lang.Long| | | | 
| ssl-bundle| java.lang.String| | | | 
### grpc.client.in-process
**Class:** `grpcstarter.client.GrpcClientProperties$InProcess`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| name| java.lang.String| | | | 
### grpc.client.refresh
**Class:** `grpcstarter.client.GrpcClientProperties$Refresh`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| enabled| java.lang.Boolean| | | | 
### grpc.client.retry
**Class:** `grpcstarter.client.GrpcClientProperties$Retry`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| enabled| java.lang.Boolean| | | | 
| max-retry-attempts| java.lang.Integer| | | | 
| per-rpc-buffer-limit| org.springframework.util.unit.DataSize| | | | 
| retry-buffer-size| org.springframework.util.unit.DataSize| | | | 
### grpc.client.tls
**Class:** `grpcstarter.client.GrpcClientProperties$Tls`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|

## grpc-server-boot-autoconfigure
### grpc.server.tls.key-manager
**Class:** `grpcstarter.server.GrpcServerProperties$Tls$KeyManager`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| cert-chain| org.springframework.core.io.Resource| | | | 
| private-key| org.springframework.core.io.Resource| | | | 
| private-key-password| java.lang.String| | | | 
### grpc.server.tls.trust-manager
**Class:** `grpcstarter.server.GrpcServerProperties$Tls$TrustManager`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| root-certs| org.springframework.core.io.Resource| | | | 
### grpc.server.health.datasource
**Class:** `grpcstarter.server.GrpcServerProperties$Health$DataSource`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| enabled| java.lang.Boolean| | | | 
| service| java.lang.String| | | | 
| timeout| java.lang.Integer| | | | 
### grpc.server.health.redis
**Class:** `grpcstarter.server.GrpcServerProperties$Health$Redis`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| enabled| java.lang.Boolean| | | | 
| service| java.lang.String| | | | 
### grpc.server
**Class:** `grpcstarter.server.GrpcServerProperties`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| enable-empty-server| java.lang.Boolean| | | | 
| enabled| java.lang.Boolean| | | | 
| max-inbound-message-size| org.springframework.util.unit.DataSize| | | | 
| max-inbound-metadata-size| org.springframework.util.unit.DataSize| | | | 
| port| java.lang.Integer| | | | 
| shutdown-timeout| java.lang.Long| | | | 
| ssl-bundle| java.lang.String| | | | 
### grpc.server.channelz
**Class:** `grpcstarter.server.GrpcServerProperties$Channelz`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| enabled| java.lang.Boolean| | | | 
| max-page-size| java.lang.Integer| | | | 
### grpc.server.exception-handling
**Class:** `grpcstarter.server.GrpcServerProperties$ExceptionHandling`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| default-exception-advice-enabled| java.lang.Boolean| | | | 
| enabled| java.lang.Boolean| | | | 
### grpc.server.health
**Class:** `grpcstarter.server.GrpcServerProperties$Health`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| enabled| java.lang.Boolean| | | | 
### grpc.server.in-process
**Class:** `grpcstarter.server.GrpcServerProperties$InProcess`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| name| java.lang.String| | | | 
### grpc.server.reflection
**Class:** `grpcstarter.server.GrpcServerProperties$Reflection`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| enabled| java.lang.Boolean| | | | 
### grpc.server.response
**Class:** `grpcstarter.server.GrpcServerProperties$Response`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| max-description-length| java.lang.Integer| | | | 
### grpc.server.tls
**Class:** `grpcstarter.server.GrpcServerProperties$Tls`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|

## grpc-metrics
### grpc.metrics
**Class:** `grpcstarter.extensions.metrics.GrpcMetricsProperties`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| enabled| java.lang.Boolean| whether to enable metrics, default is \{@code true}| true| | 
### grpc.metrics.client
**Class:** `grpcstarter.extensions.metrics.GrpcMetricsProperties$Client`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| enabled| java.lang.Boolean| whether to enable client metrics, default is \{@code true}| true| | 
| order| java.lang.Integer| The order of the client metrics interceptor. Default is \{@code 0}.| 0| | 
### grpc.metrics.server
**Class:** `grpcstarter.extensions.metrics.GrpcMetricsProperties$Server`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| enabled| java.lang.Boolean| whether to enable server metrics, default is \{@code true}| true| | 
| order| java.lang.Integer| The order of the server metrics interceptor. Default is \{@code 0}.| 0| | 

## grpc-test
### grpc.test
**Class:** `grpcstarter.extensions.test.GrpcTestProperties`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| enabled| java.lang.Boolean| | | | 
### grpc.test.server
**Class:** `grpcstarter.extensions.test.GrpcTestProperties$Server`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| enabled| java.lang.Boolean| | | | 
| port-type| grpcstarter.extensions.test.GrpcTestProperties$PortType| | | | 

## grpc-tracing
### grpc.tracing
**Class:** `grpcstarter.extensions.tracing.GrpcTracingProperties`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| enabled| java.lang.Boolean| whether to enable tracing, default is \{@code true}| true| | 
### grpc.tracing.client
**Class:** `grpcstarter.extensions.tracing.GrpcTracingProperties$Client`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| enabled| java.lang.Boolean| whether to enable client tracing, default is \{@code true}| true| | 
| order| java.lang.Integer| The order of the client tracing interceptor. Default is \{@code 0}.| 0| | 
### grpc.tracing.server
**Class:** `grpcstarter.extensions.tracing.GrpcTracingProperties$Server`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| enabled| java.lang.Boolean| whether to enable server tracing, default is \{@code true}| true| | 
| order| java.lang.Integer| The order of the server tracing interceptor. Default is \{@code 0}.| 0| | 

## grpc-transcoding
### grpc.transcoding
**Class:** `grpcstarter.extensions.transcoding.GrpcTranscodingProperties`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| auto-mapping| java.lang.Boolean| | | | 
| enabled| java.lang.Boolean| | | | 
| endpoint| java.lang.String| | | | 
### grpc.transcoding.print-options
**Class:** `grpcstarter.extensions.transcoding.GrpcTranscodingProperties$PrintOptions`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| add-whitespace| java.lang.Boolean| | | | 
| always-print-enums-as-ints| java.lang.Boolean| | | | 

## grpc-validation
### grpc.validation
**Class:** `grpcstarter.extensions.validation.GrpcValidationProperties`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| backend| grpcstarter.extensions.validation.GrpcValidationProperties$Backend| | | | 
| enabled| java.lang.Boolean| | | | 
### grpc.validation.client
**Class:** `grpcstarter.extensions.validation.GrpcValidationProperties$Client`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| enabled| java.lang.Boolean| | | | 
| order| java.lang.Integer| | | | 
### grpc.validation.server
**Class:** `grpcstarter.extensions.validation.GrpcValidationProperties$Server`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| enabled| java.lang.Boolean| | | | 
| order| java.lang.Integer| | | | 

